import { Button } from "antd";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const InsertTag = () => {
  const runner = useCommandRunner();

  return (
    <Button
      onClick={() =>
        runner(BuildInCommand.InsertTagIntoSender, {
          text: "企业名称",
          rawValue: "企业名称(id: 123456)",
          tooltips: "企业名称(id: 123456)",
        })
      }
    >
      插入标签
    </Button>
  );
};

export default InsertTag;
