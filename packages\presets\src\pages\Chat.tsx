import React, { useEffect } from "react";
import { useParams } from "react-router";

import NavigationBar from "@/components/navigation-bar";
import { DefaultChatLayout } from "@/layout";
import {
  BuildInCommand,
  MessageContainer,
  Sender,
  SidePanel,
  useActiveAgentCode,
  useActiveConversationId,
  useCommandRunner,
  useConversations,
} from "@cscs-agent/core";

const Chat: React.FC = () => {
  const params = useParams();
  const [conversations] = useConversations();
  const [, setActiveConversationId] = useActiveConversationId();
  const [, setActiveAgentCode] = useActiveAgentCode();
  const runner = useCommandRunner();

  useEffect(() => {
    setActiveConversationId(params.id ?? null);
    const conversation = conversations.find((i) => i.id === params.id);
    if (conversation) {
      setActiveAgentCode(conversation.currentAgentCode);
    } else {
      setActiveAgentCode(null);
    }
  }, [params.id, conversations]);

  useEffect(() => {
    return () => {
      setActiveConversationId(null);
      setActiveAgentCode(null);
      runner(BuildInCommand.CancelChatRequest);
    };
  }, [params.id]);

  return (
    <DefaultChatLayout
      main={{
        navigationBar: <NavigationBar />,
        message: (
          <div className="p-4">
            <MessageContainer />
          </div>
        ),
        sender: <Sender />,
      }}
      sidePanel={
        <div className="presets:bg-gray-100" style={{ height: "100vh" }}>
          <SidePanel />
        </div>
      }
    />
  );
};

export default Chat;
