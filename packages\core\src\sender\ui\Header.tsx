import React, { useMemo } from "react";

import { useActiveAgentConfig, useActiveAgentMenuCode, useAgentConfigs } from "@/core";

interface SenderHeaderProps {
  isHomePage: boolean;
}

const SenderHeader: React.FC<SenderHeaderProps> = (props) => {
  const { isHomePage } = props;
  const agentConfig = useActiveAgentConfig();
  const agentConfigs = useAgentConfigs();
  const [activeAgentMenuCode] = useActiveAgentMenuCode();

  const widgets = useMemo(() => {
    if (!isHomePage) {
      return agentConfig?.sender?.slots?.header?.widgets ?? [];
    }
    return agentConfigs.find((i) => i.code === activeAgentMenuCode)?.sender?.slots?.header?.widgets ?? [];
  }, [isHomePage, agentConfig, agentConfigs, activeAgentMenuCode]);

  return (
    <div className="ag:pb-2 ag:flex ag:flex-wrap">
      {widgets.map((Widget, index) => (
        <div className="ag:mr-2 ag:mt-2" key={index}>
          <Widget.component />
        </div>
      ))}
    </div>
  );
};

export default SenderHeader;
