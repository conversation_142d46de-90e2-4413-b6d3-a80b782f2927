import React from "react";

import { IMessage } from "@/types";

import MessageRenderContext from "./Context";
import PackageRender from "./PackageRender";

interface MessageRenderProps {
  data: IMessage;
}

/**
 * 解析消息内容
 * 首先依次解析每个 Package，
 */
const MessageRender: React.FC<MessageRenderProps> = (props) => {
  const { data } = props;
  const packages = data.content;

  return (
    <div className="ag:text-sm ag:text-dark">
      <MessageRenderContext.Provider value={{ message: data }}>
        {packages.map((i) => (
          <PackageRender msgPackage={i} key={i.package_id} />
        ))}
      </MessageRenderContext.Provider>
    </div>
  );
};

export default MessageRender;
