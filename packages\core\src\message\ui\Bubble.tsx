import { Flex } from "antd";
import React from "react";

import { useActiveAgentConfig } from "@/core/hooks/useAgent";
import { IMessage, MessageStatus } from "@/types";

import { MessageRender } from "../render";

interface BubbleProps {
  message: IMessage;
  messageRender?: (content: string) => React.ReactNode;
  avatar: {
    icon: React.ReactNode;
    style?: React.CSSProperties;
  };
  placement: "start" | "end";
}

const Bubble: React.FC<BubbleProps> = (props) => {
  const { message, placement } = props;

  return (
    <Flex align="start" justify={placement === "end" ? "flex-end" : "flex-start"} className="ag:w-full">
      <div
        className={`ag:max-w-[80%] ag:p-3 ag:rounded-lg ag:ml-2 ag:mr-2 ${
          placement === "end" ? "ag:bg-[rgba(37,45,62,0.06)]" : ""
        }`}
      >
        <Header />
        <MessageRender data={message} />
        {message.status === MessageStatus.Loading && <div className="ag:message-loader ag:mt-4"></div>}
        <Footer />
      </div>
    </Flex>
  );
};

const Header: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  return (
    <div>{agentConfig?.message?.slots?.header?.widgets?.map((Widget) => <Widget.component key={Widget.code} />)}</div>
  );
};

const Footer: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  return (
    <div>{agentConfig?.message?.slots?.footer?.widgets?.map((Widget) => <Widget.component key={Widget.code} />)}</div>
  );
};

export default Bubble;
