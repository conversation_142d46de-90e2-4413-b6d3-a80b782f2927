import { Divider } from "antd";
import React from "react";

import { Icon } from "@cscs-agent/icons";

interface ToolWidgetProps {
  name: string;
  result?: string;
}

const ToolWidget: React.FC<ToolWidgetProps> = (props) => {
  const { name, result } = props ?? {};

  return (
    <div className="ag:px-3 ag:py-2 ag:mb-2 ag:border ag:border-[rgba(0,0,0,0.09)] ag:rounded-md ag:w-[300px] ag:bg-white">
      <div className="ag:flex ">
        <Icon icon="Tool" style={{ color: "rgba(37, 45, 62, 0.45)" }} />
        <span className="ag:pl-3 ag:text-[rgba(37,45,62,0.65)]">调用工具：{name}</span>
      </div>
      {result && (
        <>
          <Divider type="horizontal" style={{ margin: "8px 0" }} />
          <div className="ag:mt-2 ag:text-xs">{result}</div>
        </>
      )}
    </div>
  );
};

export default ToolWidget;
