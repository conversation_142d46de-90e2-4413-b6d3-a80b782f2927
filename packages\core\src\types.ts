import Emitter from "eventemitter3";
import React from "react";

import { AgentStore } from "./core/state/store";

/** -- 配置文件 -- */

/**
 * 组件配置接口
 * 定义可配置UI组件的结构
 */
export interface WidgetConfig {
  /** 组件名称 */
  code: string;
  /** 可选的组件用途描述 */
  description?: string;
  /** 渲染组件的React函数组件 */
  component: React.FC<any>;
  /** 组件的默认属性 */
  props?: Record<string, any>;
}

/**
 * 消息插槽组件配置接口
 * 定义可配置UI组件的结构
 */
export interface MessageSlotWidgetConfig extends WidgetConfig {
  role?: Role;
}

/**
 * 智能体配置接口
 *
 * 定义智能体的完整配置结构，包括UI组件、提示词、命令和API设置
 */
export interface AgentConfig {
  /** 智能体名称 */
  name: string;
  /** 智能体代码, 不能重复，只允许使用字母 数字 - _ */
  code: string;
  /** 智能体头像地址 */
  avatar?: string;
  /** logo 地址 */
  logo?: string;
  /** 欢迎词 */
  welcome?: string;
  /** 可选的智能体用途或能力描述 */
  description?: string;
  /** 消息区域配置 */
  message?: {
    /** 在消息区域显示的组件 */
    blocks?: {
      widgets: WidgetConfig[];
    };
    /** 消息区域的插槽配置 */
    slots?: {
      /** 在头部插槽渲染的组件 */
      header?: {
        widgets: MessageSlotWidgetConfig[];
      };
      /** 在底部插槽渲染的组件 */
      footer?: {
        widgets: MessageSlotWidgetConfig[];
      };
    };
  };
  /** 可用的提示词模板 */
  prompts?: PromptConfig[];
  /** 可执行的命令 */
  commands?: CommandConfig[];
  /** 智能体的建议配置 */
  suggestions?: SuggestionConfig[];
  /** 消息发送器配置 */
  sender?: {
    /** 发送器区域的插槽配置 */
    slots?: {
      /** 在头部插槽渲染的组件 */
      header?: {
        widgets: WidgetConfig[];
      };
      /** 在底部插槽渲染的组件 */
      footer?: {
        widgets: WidgetConfig[];
      };
    };
  };
  /** 侧边面板配置 */
  sidePanel?: {
    /** 侧边面板的插槽配置 */
    slots?: {
      /** 在头部插槽渲染的组件 */
      header?: {
        widgets: MessageSlotWidgetConfig[];
      };
      /** 在底部插槽渲染的组件 */
      footer?: {
        widgets: MessageSlotWidgetConfig[];
      };
    };
    /** 在侧边面板显示的组件 */
    render: {
      widgets?: WidgetConfig[];
    };
  };
  /** API请求配置 */
  request: {
    /** 对话API请求配置 */
    chat: RequestConfig;
    /** 全局请求配置 */
    global?: RequestConfig;
  };
}

/**
 * 提示词模板配置
 * 定义可在智能体中使用的可复用提示词
 */
interface PromptConfig {
  /** 提示词模板标题 */
  title: string;
  /** 提示词模板内容/文本 */
  content: string;
}

/**
 * 命令配置
 * 定义可在智能体内触发的可执行命令
 */
interface CommandConfig {
  /** 命令名称 */
  name: string;
  /** 可选的命令功能描述 */
  description?: string;
  /** 命令触发时执行的函数 */
  action: (...params: any[]) => any;
}

/**
 * 建议配置
 * 定义可呈现给用户的快速建议
 */
interface SuggestionConfig {
  /** 建议名称 */
  name: string;
  /** 可选的建议描述 */
  description?: string;
  /** 建议内容/文本 */
  content: string;
}

/**
 * API请求配置
 * 定义HTTP请求的配置结构
 */
interface RequestConfig {
  /** API端点URL */
  url?: string;
  /** HTTP方法（GET, POST等） */
  method?: string;
  /** HTTP头信息，键值对形式 */
  headers?: Record<string, string>;
  /** 请求体数据 */
  body?: any;
}

/**
 * 智能体聊天配置
 * 整个聊天应用的顶层配置
 */
export interface AgentChatConfig {
  /** 智能体配置列表 */
  agents: AgentConfig[];
  /** 全局请求配置 */
  request: RequestConfig;
}

/** -- 消息相关类型和接口 -- */

export enum EventType {
  Start = 1000,
  Loading = 1001,
  End = 1002,

  Error = 2000,
}

/** 消息块 Chunk */
export interface MessageChunk {
  /** 唯一标识符 */
  package_id: number;
  /** 消息包类型 */
  package_type: MessagePackageType;
  /** 唯一标识符 */
  chunk_id: number;
  /** 是否为最后一个块 */
  is_last: boolean;
  /** 消息内容 */
  data: string;
  /** 事件ID */
  event_id: number;
  /** 事件类型 */
  event_type: EventType;
}

/**
 * 消息包类型
 * 定义消息内容包的可能类型
 */
export enum MessagePackageType {
  /** 纯文本类型 */
  Text = 0,
  /** 结构化数据类型 */
  Structured = 1,
  /** 思维过程 */
  Thinking = 2,
}

/**
 * 角色枚举
 * 定义对话中可能的角色
 */
export enum Role {
  /** 用户发送的消息 */
  HUMAN = "human",
  /** 智能体/AI发送的消息 */
  AI = "ai",
  /** 系统消息（对用户不可见） */
  SYSTEM = "system",
}

/**
 * 消息命令类型枚举
 * 定义可包含在消息中的命令类型
 */
enum MessageCommandType {
  /** 调用函数的命令 */
  Call = "call",
}

/**
 * 消息命令接口
 * 定义可嵌入消息中的命令结构
 */
interface MessageCommand {
  /** 命令类型 */
  type: MessageCommandType;
  /** 要执行的命令名称 */
  name: string;
  /** 传递给命令的参数 */
  params: Record<string, any>;
}

/**
 * 结构化消息包接口
 * 定义包含命令的消息结构
 */
export interface StructuredMessagePackage {
  type: "header" | "command";
  /** 要执行的命令列表 */
  commands?: MessageCommand[];
  conversation_id?: string;
  message_id?: string;
}

export interface HeaderMessagePackage {
  type: "header";
  /** 要执行的命令列表 */
  conversation_id: string;
  message_id: string;
}

/** 消息包状态 */
export enum MessagePackageStatus {
  /** 消息包正在加载 */
  Loading = 0,
  /** 消息包已完成加载 */
  Finished = 1,
  /** 消息包加载出错 */
  Error = 2,
}

/**
 * 消息包
 * 定义消息中单个内容包的结构
 */
export interface IMessagePackage {
  /** 包的唯一标识符 */
  package_id: number;
  /** 消息包类型 */
  package_type: MessagePackageType;
  /** 状态 */
  status: MessagePackageStatus;
  /** 内容数据 - 可以是纯文本或结构化数据 */
  data: string;
}

/**
 * 消息接口
 * 定义对话中完整消息的结构
 */

export enum MessageStatus {
  /** 消息正在加载 */
  Loading = "loading",
  /** 消息已完成加载 */
  Finished = "finished",
  /** 消息加载出错 */
  Error = "error",
  /** 消息被取消 */
  Cancelled = "cancelled",
}

export interface IMessage {
  /** 消息的唯一标识符 */
  id: string;
  /** 组成消息的内容包数组 */
  content: IMessagePackage[];
  /** 消息发送者的角色 */
  role: Role;
  /** 消息的状态 */
  status: MessageStatus;
  /** AgentCode */
  agentCode: string;
  /** 是否为最新消息 */
  isFresh?: boolean;
}

export interface IMessageRenderContextValue {
  message: IMessage;
}

/** AgentChatContext */

export interface AgentChatContextValue {
  store: AgentStore;
  agentId: string;
  config: AgentChatConfig;
}

/** -- 命令模块相关类型和接口 -- */

/**
 * 命令回调函数类型
 * 定义命令触发时执行的回调函数类型
 */
export type CommandCallback = (params: any) => any;

/**
 * 命令取消订阅函数类型
 * 定义取消命令订阅的函数类型
 */
export type CommandUnsubscribe = () => void;

/**
 * 命令发射器接口
 * 定义命令事件发射器的类型
 */
export interface CommandEmitter extends Emitter {}

/**
 * 命令订阅选项接口
 * 定义订阅命令时的可选配置
 */
export interface CommandSubscribeOptions {
  /** 是否只执行一次 */
  once?: boolean;
  /** 优先级，数字越大优先级越高 */
  priority?: number;
}

/**
 * 命令注册接口
 * 定义注册到系统的命令结构
 */
export interface RegisteredCommand {
  /** 命令名称 */
  name: string;
  /** 命令描述 */
  description?: string;
  /** 命令分类 */
  category?: string;
  /** 命令处理函数 */
  handler: CommandCallback;
}

/** -- Block -- */

export enum BlockType {
  Text = "text",
  Embedded = "embedded",
  Thinking = "thinking",
}

export enum BlockStatus {
  Loading = "loading",
  Finished = "finished",
  Error = "error",
}

export interface BlockConfig {
  id: string;
  type: BlockType;
  status: BlockStatus;
  content: string; // 文本 或者 xml
  start: number;
  end: number;
}

/** -- Command -- */

export enum BuildInCommand {
  OpenSidePanel = "open_side_panel",
  CloseSidePanel = "close_side_panel",
  RenderSidePanel = "render_side_panel",
  SendMessage = "send_message",
  CancelChatRequest = "cancel_chat_request",
  NewConversationCreated = "new_conversation_created",
  InsertTextIntoSender = "insert_text_into_sender",
  InsertTagIntoSender = "insert_tag_into_sender",
  InsertEditableTagIntoSender = "insert_editable_tag_into_sender",
}

export interface IOpenSidePanelCommandParams {
  widgetCode: string;
  width?: number;
  widgetProps?: Record<string, unknown>;
}

export interface ISendMessageParams {
  message: string;
  conversationId?: string;
  agentCode?: string;
}

export interface IRenderSidePanelParams {
  widgetCode: string;
  width?: number;
  widgetProps?: Record<string, unknown>;
}

export interface IInsertTextIntoSenderParams {
  text: string;
}

export interface IInsertTagIntoSenderParams {
  text: string;
  rawValue: string;
  tooltips?: string;
}

export interface IInsertEditableTagIntoSenderParams {
  text: string;
}

export type IBaseCommandParams =
  | IOpenSidePanelCommandParams
  | ISendMessageParams
  | IRenderSidePanelParams
  | IInsertTextIntoSenderParams
  | Record<string, unknown>;

/** -- Conversation -- */

export interface IConversation {
  id: string;
  title: string;
  // 当前会话正在使用的智能体
  currentAgentCode: string;
  group: string;
}

/** -- Response -- */
export interface StandardResponse<T = unknown> {
  code: number;
  data: T;
  pagination: {
    total: number;
    page: number;
    size: number;
    page_total: number;
  };
}

export interface StandardErrorResponse {
  code: number;
  message: string;
}

/**  -- 接口返回数据类型定义 -- */

/** 会话历史 */

interface ConversationData {
  title: string;
  id: string;
  current_agent_code: string;
  updated_at: string;
}

export type ConversationHistoryResponse = StandardResponse<ConversationData[]>;
