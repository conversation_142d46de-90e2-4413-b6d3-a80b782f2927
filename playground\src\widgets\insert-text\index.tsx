import { Button } from "antd";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const template = `生成一个页面，查询数据库中的<embedded-editable-tag>企业信息表</embedded-editable-tag>，
在页面列表中展示 <embedded-editable-tag>企业名称</embedded-editable-tag>、<embedded-editable-tag>企业性质</embedded-editable-tag>、<embedded-editable-tag>统一社会信用代码</embedded-editable-tag>。
`;

const InsertText = () => {
  const runner = useCommandRunner();

  return (
    <Button
      onClick={() =>
        runner(BuildInCommand.InsertTextIntoSender, {
          text: template,
        })
      }
    >
      插入模板
    </Button>
  );
};

export default InsertText;
