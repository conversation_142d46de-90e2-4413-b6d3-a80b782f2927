import React, { useContext, useEffect } from "react";
import { useParams } from "react-router";

import { DefaultAgentLayout } from "@/layout";
import { AgentChatContext, Sender, useActiveAgentMenuCode } from "@cscs-agent/core";

const AgentHome: React.FC = () => {
  const [activeAgentMenuCode, setActiveAgentMenuCode] = useActiveAgentMenuCode();
  const params = useParams();
  const { config } = useContext(AgentChatContext);

  useEffect(() => {
    if (params.code === activeAgentMenuCode) return;
    const agents = config.agents ?? [];
    const code = agents.find((i) => i.code === params.code)?.code;
    if (code) {
      setActiveAgentMenuCode(code);
    }
  }, [params]);

  useEffect(() => {
    return () => {
      setActiveAgentMenuCode(null);
    };
  }, []);

  return (
    <DefaultAgentLayout
      main={{
        sender: <Sender isNewConversation={true} />,
      }}
    />
  );
};

export default AgentHome;
