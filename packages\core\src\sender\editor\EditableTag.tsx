import { Tooltip } from "antd";
import React from "react";

import { Node, NodeViewProps, mergeAttributes } from "@tiptap/core";
import { NodeViewContent, NodeViewWrapper } from "@tiptap/react";
import { ReactNodeViewRenderer } from "@tiptap/react";

interface EditableTagProps {
  text: string;
  rawValue: string;
  tooltips?: string;
}

/**
 * Tag 内元素设置 contentEditable，删除行内标签后面文本时自动 focus 到标签内。
 */
const EditableTagComponent: React.FC<NodeViewProps> = (props) => {
  const { node } = props;

  const { tooltips } = node.attrs as EditableTagProps;

  return (
    <NodeViewWrapper as="span">
      <Tooltip title={tooltips}>
        <span className="ag:px-2 ag:py-1 ag:rounded-md ag:bg-[rgba(0,0,0,0.05)] ag:text-[rgba(0,0,0,0.65)]">
          <NodeViewContent as="span" />
        </span>
      </Tooltip>
    </NodeViewWrapper>
  );
};

const EditableTag = Node.create({
  name: "EditableTag",
  content: "text*", // 使标签自动 focus
  group: "inline",
  inline: true,

  parseHTML() {
    return [
      {
        tag: "embedded-editable-tag",
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ["embedded-editable-tag", mergeAttributes(HTMLAttributes)];
  },

  addNodeView() {
    return ReactNodeViewRenderer(EditableTagComponent as any, {});
  },
});

export default EditableTag;
